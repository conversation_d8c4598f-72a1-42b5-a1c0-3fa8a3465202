/**
 * Live2D Expression Hook
 * Handles Live2D model expressions with clean API
 */

import { useCallback } from 'react';
import { ModelInfo } from '@/lib/live2d/types';

/**
 * Return type for useLive2DExpression hook
 */
export interface UseLive2DExpressionReturn {
  setExpression: (value: string | number, logMessage?: string) => void;
  resetExpression: () => void;
}

/**
 * Custom hook for handling Live2D model expressions
 * Provides clean API for setting and resetting expressions
 */
export const useLive2DExpression = (): UseLive2DExpressionReturn => {
  /**
   * Set expression for Live2D model
   * @param expressionValue - Expression name (string) or index (number)
   * @param logMessage - Optional message to log on success
   */
  const setExpression = useCallback((
    expressionValue: string | number,
    logMessage?: string,
  ) => {
    try {
      const lappAdapter = (window as any).getLAppAdapter?.();
      if (!lappAdapter) {
        console.warn('LAppAdapter not available');
        return;
      }

      if (typeof expressionValue === 'string') {
        // Set expression by name
        lappAdapter.setExpression(expressionValue);
      } else if (typeof expressionValue === 'number') {
        // Set expression by index
        const expressionName = lappAdapter.getExpressionName(expressionValue);
        if (expressionName) {
          lappAdapter.setExpression(expressionName);
        } else {
          console.warn(`Expression index ${expressionValue} not found`);
          return;
        }
      }
      
      if (logMessage) {
        console.log(logMessage);
      }
    } catch (error) {
      console.error('Failed to set expression:', error);
    }
  }, []);

  /**
   * Reset expression to default
   * Uses the model's default emotion if available, otherwise uses first expression
   */
  const resetExpression = useCallback(() => {
    try {
      const lappAdapter = (window as any).getLAppAdapter?.();
      if (!lappAdapter) {
        console.warn('LAppAdapter not available');
        return;
      }

      // Check if model is loaded and has expressions
      const model = lappAdapter.getModel();
      if (!model || !model._modelSetting) {
        console.log('Model or model settings not loaded yet, skipping expression reset');
        return;
      }

      // Try to get model info from global state or use first expression as fallback
      const expressionCount = lappAdapter.getExpressionCount();
      if (expressionCount > 0) {
        const defaultExpressionName = lappAdapter.getExpressionName(0);
        if (defaultExpressionName) {
          setExpression(defaultExpressionName, 'Reset expression to default');
        }
      }
    } catch (error) {
      console.error('Failed to reset expression:', error);
    }
  }, [setExpression]);

  return {
    setExpression,
    resetExpression,
  };
};

/**
 * Helper function to reset expression with specific model info
 * @param modelInfo - Model information containing default emotion
 * @param setExpressionFn - Function to set expression
 */
export const resetExpressionWithModelInfo = (
  modelInfo: ModelInfo | undefined,
  setExpressionFn: (value: string | number, logMessage?: string) => void
) => {
  try {
    const lappAdapter = (window as any).getLAppAdapter?.();
    if (!lappAdapter) {
      console.warn('LAppAdapter not available');
      return;
    }

    // Check if model is loaded and has expressions
    const model = lappAdapter.getModel();
    if (!model || !model._modelSetting) {
      console.log('Model or model settings not loaded yet, skipping expression reset');
      return;
    }

    // If model has a default emotion defined, use it
    if (modelInfo?.defaultEmotion !== undefined) {
      setExpressionFn(
        modelInfo.defaultEmotion,
        `Reset expression to default: ${modelInfo.defaultEmotion}`,
      );
    } else {
      // Check if model has any expressions before trying to get the first one
      const expressionCount = lappAdapter.getExpressionCount();
      if (expressionCount > 0) {
        const defaultExpressionName = lappAdapter.getExpressionName(0);
        if (defaultExpressionName) {
          setExpressionFn(defaultExpressionName, 'Reset expression to first available');
        }
      }
    }
  } catch (error) {
    console.error('Failed to reset expression with model info:', error);
  }
};

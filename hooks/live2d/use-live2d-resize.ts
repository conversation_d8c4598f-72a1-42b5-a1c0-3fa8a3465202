/**
 * Live2D Resize Hook
 * Handles Live2D model resizing and scaling with smooth animations
 */

import { useEffect, useCallback, RefObject, useRef } from 'react';
import { ModelInfo } from '@/lib/live2d/types';
import { LAppDelegate } from '@cubismsdksamples/lappdelegate';
import { LAppLive2DManager } from '@cubismsdksamples/lapplive2dmanager';

// Constants for model scaling behavior
const MIN_SCALE = 0.1;
const MAX_SCALE = 5.0;
const EASING_FACTOR = 0.3; // Controls animation smoothness
const WHEEL_SCALE_STEP = 0.03; // Scale change per wheel tick
const DEFAULT_SCALE = 1.0; // Default scale if not specified

interface UseLive2DResizeProps {
  containerRef: RefObject<HTMLDivElement>;
  modelInfo?: ModelInfo;
  showSidebar?: boolean; // Sidebar collapse state
}

interface UseLive2DResizeReturn {
  canvasRef: RefObject<HTMLCanvasElement>;
  handleResize: () => void;
}

/**
 * Applies scale to both model and view matrices
 * @param scale - The scale value to apply
 */
export const applyScale = (scale: number) => {
  try {
    const manager = LAppLive2DManager.getInstance();
    if (!manager) return;

    const model = manager.getModel(0);
    if (!model) return;

    // Apply scale to model matrix
    (model as any)._modelMatrix.scale(scale, scale);
  } catch (error) {
    console.debug('Model not ready for scaling yet');
  }
};

/**
 * Hook to handle Live2D model resizing and scaling
 * Provides smooth scaling animation and window resize handling
 */
export const useLive2DResize = ({
  containerRef,
  modelInfo,
  showSidebar,
}: UseLive2DResizeProps): UseLive2DResizeReturn => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const currentScaleRef = useRef<number>(DEFAULT_SCALE);
  const targetScaleRef = useRef<number>(DEFAULT_SCALE);
  const animationFrameRef = useRef<number | null>(null);

  /**
   * Smooth scale animation function
   */
  const animateScale = useCallback(() => {
    const current = currentScaleRef.current;
    const target = targetScaleRef.current;
    const diff = target - current;

    if (Math.abs(diff) < 0.001) {
      currentScaleRef.current = target;
      animationFrameRef.current = null;
      return;
    }

    const newScale = current + diff * EASING_FACTOR;
    currentScaleRef.current = newScale;
    applyScale(newScale);

    animationFrameRef.current = requestAnimationFrame(animateScale);
  }, []);

  /**
   * Set target scale and start animation
   */
  const setTargetScale = useCallback((scale: number) => {
    const clampedScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale));
    targetScaleRef.current = clampedScale;

    if (animationFrameRef.current === null) {
      animationFrameRef.current = requestAnimationFrame(animateScale);
    }
  }, [animateScale]);

  /**
   * Handle canvas resize
   */
  const handleResize = useCallback(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    
    if (!canvas || !container) return;

    try {
      const delegate = LAppDelegate.getInstance();
      if (delegate) {
        delegate.onResize();
      }
    } catch (error) {
      console.debug('Delegate not ready for resize yet');
    }
  }, [containerRef]);

  /**
   * Handle wheel events for scaling (if scrollToResize is enabled)
   */
  const handleWheel = useCallback((event: WheelEvent) => {
    if (!modelInfo?.scrollToResize) return;

    event.preventDefault();
    const delta = event.deltaY > 0 ? -WHEEL_SCALE_STEP : WHEEL_SCALE_STEP;
    const newScale = targetScaleRef.current + delta;
    setTargetScale(newScale);
  }, [modelInfo?.scrollToResize, setTargetScale]);

  /**
   * Initialize canvas and set up event listeners
   */
  useEffect(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    
    if (!canvas || !container) return;

    // Set up resize observer
    const resizeObserver = new ResizeObserver(() => {
      handleResize();
    });

    resizeObserver.observe(container);

    // Set up wheel event listener
    if (modelInfo?.scrollToResize) {
      canvas.addEventListener('wheel', handleWheel, { passive: false });
    }

    // Set up window resize listener
    window.addEventListener('resize', handleResize);

    return () => {
      resizeObserver.disconnect();
      canvas.removeEventListener('wheel', handleWheel);
      window.removeEventListener('resize', handleResize);
      
      // Cancel any ongoing animation
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [containerRef, modelInfo?.scrollToResize, handleWheel, handleResize]);

  /**
   * Handle sidebar changes
   */
  useEffect(() => {
    // Trigger resize when sidebar state changes
    const timeoutId = setTimeout(handleResize, 100);
    return () => clearTimeout(timeoutId);
  }, [showSidebar, handleResize]);

  /**
   * Initialize scale when model info changes
   */
  useEffect(() => {
    if (modelInfo?.kScale) {
      const initialScale = modelInfo.initialScale || modelInfo.kScale;
      currentScaleRef.current = initialScale;
      targetScaleRef.current = initialScale;
      applyScale(initialScale);
    }
  }, [modelInfo?.kScale, modelInfo?.initialScale]);

  return {
    canvasRef,
    handleResize,
  };
};

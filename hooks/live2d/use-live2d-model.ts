/**
 * Live2D Model Hook
 * Handles Live2D model loading, interactions, and event handling
 */

import { useEffect, useRef, useCallback, RefObject } from 'react';
import { ModelInfo, Position, InteractionHandlers } from '@/lib/live2d/types';
import { useLive2DConfig, useLive2DInteraction } from '@/lib/live2d/store';
import { updateModelConfig } from '@cubismsdksamples/lappdefine';
import { LAppDelegate } from '@cubismsdksamples/lappdelegate';
import { LAppView } from '@cubismsdksamples/lappview';
// Import main.ts to ensure initializeLive2D is available on window
import '@cubismsdksamples/main';

interface UseLive2DModelProps {
  modelInfo: ModelInfo | undefined;
  canvasRef: RefObject<HTMLCanvasElement>;
}

interface UseLive2DModelReturn {
  position: Position;
  isDragging: boolean;
  handlers: InteractionHandlers;
}

// Thresholds for tap vs drag detection
const TAP_DURATION_THRESHOLD_MS = 200; // Max duration for a tap
const DRAG_DISTANCE_THRESHOLD_PX = 5; // Min distance to be considered a drag

/**
 * Parse model URL to extract base URL, model directory, and filename
 */
function parseModelUrl(url: string): { baseUrl: string; modelDir: string; modelFileName: string } {
  try {
    const urlObj = new URL(url);
    const { pathname } = urlObj;

    const lastSlashIndex = pathname.lastIndexOf('/');
    if (lastSlashIndex === -1) {
      throw new Error('Invalid model URL format');
    }

    const fullFileName = pathname.substring(lastSlashIndex + 1);
    const modelFileName = fullFileName.replace('.model3.json', '');

    const secondLastSlashIndex = pathname.lastIndexOf('/', lastSlashIndex - 1);
    if (secondLastSlashIndex === -1) {
      throw new Error('Invalid model URL format');
    }

    const modelDir = pathname.substring(secondLastSlashIndex + 1, lastSlashIndex);
    const baseUrl = `${urlObj.protocol}//${urlObj.host}${pathname.substring(0, secondLastSlashIndex + 1)}`;

    return { baseUrl, modelDir, modelFileName };
  } catch (error) {
    console.error('Failed to parse model URL:', error);
    throw error;
  }
}

/**
 * Initialize Live2D with model configuration
 */
const initializeWithModel = async (modelInfo: ModelInfo): Promise<boolean> => {
  try {
    const { baseUrl, modelDir, modelFileName } = parseModelUrl(modelInfo.url);

    // Get canvas element from DOM
    const canvas = document.getElementById('canvas') as HTMLCanvasElement;
    if (!canvas) {
      throw new Error('Canvas element not found');
    }

    // Ensure canvas has proper dimensions
    if (canvas.width === 0 || canvas.height === 0) {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width || 800;
      canvas.height = rect.height || 600;
    }

    // Update model configuration BEFORE initializing
    updateModelConfig(baseUrl, modelDir, modelFileName, modelInfo.kScale || 1.0);



    // Initialize Live2D (will skip if already initialized)
    if (!window.initializeLive2D) {
      throw new Error('initializeLive2D function not available on window object');
    }

    const initResult = window.initializeLive2D();
    if (initResult instanceof Promise) {
      await initResult;
    }

    // Re-initialize canvas for the current component instance
    const { LAppGlManager } = await import('@cubismsdksamples/lappglmanager');
    const glManager = LAppGlManager.getInstance();
    if (glManager && (glManager as any).initializeCanvas) {
      (glManager as any).initializeCanvas();
    }

    // Wait a bit for initialization to complete
    await new Promise(resolve => setTimeout(resolve, 100));

    // Get or create the manager and load the scene with new configuration
    const { LAppLive2DManager } = await import('@cubismsdksamples/lapplive2dmanager');
    const manager = LAppLive2DManager.getInstance();
    if (manager && manager.changeScene) {
      // Always reload the scene to ensure the new model configuration is used
      manager.changeScene(0);
    }

    return true;
  } catch (error) {
    console.error('Failed to initialize Live2D with model:', error);
    return false;
  }
};

/**
 * Custom hook for Live2D model management and interactions
 */
export const useLive2DModel = ({
  modelInfo,
  canvasRef,
}: UseLive2DModelProps): UseLive2DModelReturn => {
  const { setIsLoading, setError } = useLive2DConfig();
  const { 
    isDragging, 
    position, 
    setDragging, 
    setPosition 
  } = useLive2DInteraction();

  // Interaction state refs
  const startPositionRef = useRef<Position>({ x: 0, y: 0 });
  const startTimeRef = useRef<number>(0);
  const hasDraggedRef = useRef<boolean>(false);

  /**
   * Handle model loading when modelInfo changes
   */
  useEffect(() => {
    if (!modelInfo || !canvasRef.current) return;

    const loadModel = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Ensure canvas is properly mounted and has dimensions
        const canvas = canvasRef.current;
        if (!canvas || canvas.width === 0 || canvas.height === 0) {
          setTimeout(() => loadModel(), 100);
          return;
        }

        const success = await initializeWithModel(modelInfo);
        if (!success) {
          setError({
            type: 'MODEL_LOAD',
            message: 'Failed to load Live2D model',
            recoverable: true,
          });
        }
      } catch (error) {
        setError({
          type: 'MODEL_LOAD',
          message: error instanceof Error ? error.message : 'Unknown error loading model',
          details: error,
          recoverable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    // Add a small delay to ensure DOM is ready
    const timeoutId = setTimeout(loadModel, 100);
    return () => clearTimeout(timeoutId);
  }, [modelInfo, canvasRef, setIsLoading, setError]);

  /**
   * Handle mouse/pointer down events
   */
  const handleMouseDown = useCallback((e: React.MouseEvent | React.PointerEvent) => {
    if (!modelInfo?.pointerInteractive) return;

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const clientX = e.clientX;
    const clientY = e.clientY;

    const startPos = {
      x: clientX - rect.left,
      y: clientY - rect.top,
    };

    startPositionRef.current = startPos;
    startTimeRef.current = Date.now();
    hasDraggedRef.current = false;
    setDragging(true);
  }, [modelInfo?.pointerInteractive, canvasRef, setDragging]);

  /**
   * Handle mouse move events
   */
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging || !modelInfo?.pointerInteractive) return;

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const currentPos = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };

    const deltaX = currentPos.x - startPositionRef.current.x;
    const deltaY = currentPos.y - startPositionRef.current.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    if (distance > DRAG_DISTANCE_THRESHOLD_PX) {
      hasDraggedRef.current = true;
      setPosition(currentPos);

      // Update Live2D model position
      try {
        const delegate = LAppDelegate.getInstance();
        const view = (delegate as { _view?: LAppView })._view;
        if (view) {
          view.onTouchesMoved(currentPos.x, currentPos.y);
        }
      } catch {
        console.debug('Delegate not ready for drag yet');
      }
    }
  }, [isDragging, modelInfo?.pointerInteractive, canvasRef, setPosition]);

  /**
   * Handle mouse up events
   */
  const handleMouseUp = useCallback((e: React.MouseEvent) => {
    if (!isDragging) return;

    const duration = Date.now() - startTimeRef.current;
    const wasTap = !hasDraggedRef.current && duration < TAP_DURATION_THRESHOLD_MS;

    if (wasTap && modelInfo?.pointerInteractive) {
      const rect = canvasRef.current?.getBoundingClientRect();
      if (rect) {
        const tapPos = {
          x: e.clientX - rect.left,
          y: e.clientY - rect.top,
        };

        // Handle tap interaction
        try {
          const delegate = LAppDelegate.getInstance();
          const view = (delegate as { _view?: LAppView })._view;
          if (view) {
            view.onTouchesEnded(tapPos.x, tapPos.y);
          }
        } catch {
          console.debug('Delegate not ready for tap yet');
        }
      }
    }

    setDragging(false);
    hasDraggedRef.current = false;
  }, [isDragging, modelInfo?.pointerInteractive, canvasRef, setDragging]);

  /**
   * Handle mouse leave events
   */
  const handleMouseLeave = useCallback(() => {
    if (isDragging) {
      setDragging(false);
      hasDraggedRef.current = false;
    }
  }, [isDragging, setDragging]);

  /**
   * Create interaction handlers object
   */
  const handlers: InteractionHandlers = {
    onMouseDown: handleMouseDown,
    onMouseMove: handleMouseMove,
    onMouseUp: handleMouseUp,
    onMouseLeave: handleMouseLeave,
  };

  return {
    position,
    isDragging,
    handlers,
  };
};

/**
 * Live2D Test Page
 * Test page to validate the Live2D migration
 */

'use client';

import React, { useState } from 'react';
import { Live2DClient, SimpleLive2D, useLive2DAPI, Live2DUtils } from '@/components/live2d';
import { ModelInfo } from '@/lib/live2d/types';

export default function Live2DTestPage() {
  const [selectedExample, setSelectedExample] = useState<'simple' | 'advanced' | 'api'>('simple');
  const [modelUrl, setModelUrl] = useState(
    "http://127.0.0.1:12393/live2d-models/elaina/LSS.model3.json"
  );
  
  const api = useLive2DAPI({
    enableLogging: true,
    errorHandler: (error) => {
      console.error('Live2D Test Error:', error);
    },
  });

  const [modelInfo, setModelInfo] = useState<ModelInfo>({
    url: modelUrl,
    kScale: 1.0,
    pointerInteractive: true,
    scrollToResize: true,
    initialXshift: 0,
    initialYshift: 0,
  });

  const handleModelUrlChange = (url: string) => {
    setModelUrl(url);
    setModelInfo(prev => ({ ...prev, url }));
  };

  const renderSimpleExample = () => (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Simple Live2D Example</h2>
      <div className="space-y-2">
        <label className="block text-sm font-medium">Model URL:</label>
        <input
          type="text"
          value={modelUrl}
          onChange={(e) => handleModelUrlChange(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded"
          placeholder="Enter model URL..."
        />
        <p className="text-xs text-gray-500">
          Valid: {Live2DUtils.isValidModelUrl(modelUrl) ? '✅' : '❌'}
        </p>
      </div>
      <div className="w-full h-96 border border-gray-300 rounded-lg bg-gray-50">
        <SimpleLive2D
          modelUrl={modelUrl}
          className="w-full h-full"
        />
      </div>
    </div>
  );

  const renderAdvancedExample = () => (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Advanced Live2D Example</h2>
      
      {/* Model Configuration */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="block text-sm font-medium">Model URL:</label>
          <input
            type="text"
            value={modelInfo.url}
            onChange={(e) => setModelInfo(prev => ({ ...prev, url: e.target.value }))}
            className="w-full p-2 border border-gray-300 rounded text-sm"
          />
        </div>
        
        <div className="space-y-2">
          <label className="block text-sm font-medium">Scale:</label>
          <input
            type="range"
            min="0.5"
            max="3.0"
            step="0.1"
            value={modelInfo.kScale}
            onChange={(e) => setModelInfo(prev => ({ ...prev, kScale: parseFloat(e.target.value) }))}
            className="w-full"
          />
          <span className="text-sm text-gray-600">{modelInfo.kScale.toFixed(1)}</span>
        </div>
      </div>

      {/* Settings */}
      <div className="flex flex-wrap gap-4">
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={modelInfo.pointerInteractive}
            onChange={(e) => setModelInfo(prev => ({ ...prev, pointerInteractive: e.target.checked }))}
          />
          <span className="text-sm">Pointer Interactive</span>
        </label>
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={modelInfo.scrollToResize}
            onChange={(e) => setModelInfo(prev => ({ ...prev, scrollToResize: e.target.checked }))}
          />
          <span className="text-sm">Scroll to Resize</span>
        </label>
      </div>

      {/* Live2D Display */}
      <div className="w-full h-96 border border-gray-300 rounded-lg bg-gray-50">
        <Live2DClient
          modelInfo={modelInfo}
          className="w-full h-full"
          fallback={
            <div className="flex items-center justify-center w-full h-full bg-red-50">
              <div className="text-center">
                <div className="text-red-600 text-lg font-semibold mb-2">Error</div>
                <div className="text-red-500 text-sm">Failed to load Live2D model</div>
              </div>
            </div>
          }
        />
      </div>
    </div>
  );

  const renderAPIExample = () => {
    // Safely get API data with fallbacks
    const expressions = api.isReady() ? api.getExpressions() : [];
    const interactionState = api.getInteractionState();
    const currentModelInfo = api.getModelInfo();

    return (
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">API Example</h2>
        
        {/* API Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Expression Controls</h3>
            <div className="flex flex-wrap gap-2">
              {expressions.length > 0 ? (
                expressions.map((expression) => (
                  <button
                    key={expression}
                    onClick={() => api.setExpression(expression)}
                    className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-sm"
                  >
                    {expression}
                  </button>
                ))
              ) : (
                <p className="text-gray-500 text-sm">No expressions available</p>
              )}
              <button
                onClick={() => api.resetExpression()}
                className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors text-sm"
              >
                Reset
              </button>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-medium">Status</h3>
            <div className="text-sm space-y-1">
              <p><strong>SDK Ready:</strong> {api.isReady() ? '✅' : '❌'}</p>
              <p><strong>Is Dragging:</strong> {interactionState.isDragging ? '✅' : '❌'}</p>
              <p><strong>Position:</strong> ({interactionState.position.x.toFixed(0)}, {interactionState.position.y.toFixed(0)})</p>
              <p><strong>Model Loaded:</strong> {currentModelInfo ? '✅' : '❌'}</p>
            </div>
          </div>
        </div>

        {/* Live2D Display */}
        <div className="w-full h-96 border border-gray-300 rounded-lg bg-gray-50">
          <Live2DClient
            modelUrl={modelUrl}
            className="w-full h-full"
          />
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Live2D Migration Test</h1>
        <p className="text-gray-600 mb-4">
          This page tests the migrated Live2D functionality from old-assets to the new Next.js structure.
        </p>
        
        {/* Example Selector */}
        <div className="flex space-x-2 mb-6">
          {[
            { key: 'simple', label: 'Simple' },
            { key: 'advanced', label: 'Advanced' },
            { key: 'api', label: 'API' },
          ].map(({ key, label }) => (
            <button
              key={key}
              onClick={() => setSelectedExample(key as any)}
              className={`px-4 py-2 rounded transition-colors ${
                selectedExample === key
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Render Selected Example */}
      <div className="mb-8">
        {selectedExample === 'simple' && renderSimpleExample()}
        {selectedExample === 'advanced' && renderAdvancedExample()}
        {selectedExample === 'api' && renderAPIExample()}
      </div>

      {/* Migration Status */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-green-800 mb-2">Migration Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
          <div className="text-green-700">✅ WebSDK copied and configured</div>
          <div className="text-green-700">✅ React components migrated</div>
          <div className="text-green-700">✅ Hooks updated with new structure</div>
          <div className="text-green-700">✅ Zustand state management</div>
          <div className="text-green-700">✅ TypeScript support</div>
          <div className="text-green-700">✅ Next.js SSR compatibility</div>
          <div className="text-green-700">✅ TailwindCSS styling</div>
          <div className="text-green-700">✅ Error handling</div>
          <div className="text-green-700">✅ Clean public API</div>
          <div className="text-green-700">✅ Electron dependencies removed</div>
        </div>
      </div>
    </div>
  );
}

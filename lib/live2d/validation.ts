/**
 * Live2D Validation and Error Handling Utilities
 * Provides validation functions and error handling helpers
 */

import { ModelInfo, Live2DError, Live2DErrorType } from './types';

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validate ModelInfo object
 */
export const validateModelInfo = (modelInfo: Partial<ModelInfo>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!modelInfo.url) {
    errors.push('Model URL is required');
  } else {
    // Validate URL format
    try {
      new URL(modelInfo.url);
    } catch {
      errors.push('Model URL is not a valid URL');
    }

    // Check if URL ends with .model3.json
    if (!modelInfo.url.endsWith('.model3.json')) {
      warnings.push('Model URL should end with .model3.json');
    }
  }

  // Validate scale values
  if (modelInfo.kScale !== undefined) {
    if (typeof modelInfo.kScale !== 'number' || modelInfo.kScale <= 0) {
      errors.push('kScale must be a positive number');
    } else if (modelInfo.kScale < 0.1 || modelInfo.kScale > 10) {
      warnings.push('kScale should typically be between 0.1 and 10');
    }
  }

  // Validate position shifts
  if (modelInfo.initialXshift !== undefined && typeof modelInfo.initialXshift !== 'number') {
    errors.push('initialXshift must be a number');
  }

  if (modelInfo.initialYshift !== undefined && typeof modelInfo.initialYshift !== 'number') {
    errors.push('initialYshift must be a number');
  }

  // Validate boolean flags
  if (modelInfo.pointerInteractive !== undefined && typeof modelInfo.pointerInteractive !== 'boolean') {
    errors.push('pointerInteractive must be a boolean');
  }

  if (modelInfo.scrollToResize !== undefined && typeof modelInfo.scrollToResize !== 'boolean') {
    errors.push('scrollToResize must be a boolean');
  }

  // Validate default emotion
  if (modelInfo.defaultEmotion !== undefined) {
    if (typeof modelInfo.defaultEmotion !== 'string' && typeof modelInfo.defaultEmotion !== 'number') {
      errors.push('defaultEmotion must be a string or number');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Validate canvas element
 */
export const validateCanvas = (canvas: HTMLCanvasElement | null): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!canvas) {
    errors.push('Canvas element is null');
    return { isValid: false, errors, warnings };
  }

  // Check if canvas is in DOM
  if (!canvas.isConnected) {
    errors.push('Canvas element is not connected to DOM');
  }

  // Check canvas dimensions
  if (canvas.width === 0 || canvas.height === 0) {
    warnings.push('Canvas has zero dimensions');
  }

  // Check WebGL support
  try {
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (!gl) {
      errors.push('WebGL is not supported');
    }
  } catch (error) {
    errors.push('Failed to get WebGL context');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Create a standardized Live2D error
 */
export const createLive2DError = (
  type: Live2DErrorType,
  message: string,
  details?: any,
  recoverable: boolean = false
): Live2DError => ({
  type,
  message,
  details,
  recoverable,
});

/**
 * Error factory functions
 */
export const createSDKInitError = (message: string, details?: any): Live2DError =>
  createLive2DError('SDK_INIT', message, details, true);

export const createModelLoadError = (message: string, details?: any): Live2DError =>
  createLive2DError('MODEL_LOAD', message, details, true);

export const createRuntimeError = (message: string, details?: any): Live2DError =>
  createLive2DError('RUNTIME', message, details, false);

export const createAudioError = (message: string, details?: any): Live2DError =>
  createLive2DError('AUDIO', message, details, true);

/**
 * Check if error is recoverable
 */
export const isRecoverableError = (error: Live2DError): boolean => {
  return error.recoverable;
};

/**
 * Get user-friendly error message
 */
export const getUserFriendlyErrorMessage = (error: Live2DError): string => {
  switch (error.type) {
    case 'SDK_INIT':
      return 'Failed to initialize Live2D. Please refresh the page and try again.';
    case 'MODEL_LOAD':
      return 'Failed to load the Live2D model. Please check the model URL and try again.';
    case 'RUNTIME':
      return 'A runtime error occurred. The Live2D model may not function properly.';
    case 'AUDIO':
      return 'Audio playback failed. Live2D will continue without audio.';
    default:
      return 'An unknown error occurred with Live2D.';
  }
};

/**
 * Log validation result
 */
export const logValidationResult = (result: ValidationResult, context: string): void => {
  if (!result.isValid) {
    console.error(`${context} validation failed:`, result.errors);
  }
  
  if (result.warnings.length > 0) {
    console.warn(`${context} validation warnings:`, result.warnings);
  }
  
  if (result.isValid && result.warnings.length === 0) {
    console.log(`${context} validation passed`);
  }
};

/**
 * Safe function execution with error handling
 */
export const safeExecute = <T>(
  fn: () => T,
  errorType: Live2DErrorType,
  errorMessage: string,
  defaultValue: T
): T => {
  try {
    return fn();
  } catch (error) {
    const live2dError = createLive2DError(errorType, errorMessage, error);
    console.error('Safe execution failed:', live2dError);
    return defaultValue;
  }
};

/**
 * Async safe function execution with error handling
 */
export const safeExecuteAsync = async <T>(
  fn: () => Promise<T>,
  errorType: Live2DErrorType,
  errorMessage: string,
  defaultValue: T
): Promise<T> => {
  try {
    return await fn();
  } catch (error) {
    const live2dError = createLive2DError(errorType, errorMessage, error);
    console.error('Async safe execution failed:', live2dError);
    return defaultValue;
  }
};

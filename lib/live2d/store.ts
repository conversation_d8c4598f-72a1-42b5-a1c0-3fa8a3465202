/**
 * Live2D State Management using Zustand
 * Simple state management for Live2D configuration and interaction state
 */

import { create } from 'zustand';
import { ModelInfo, Position, Live2DError } from './types';

/**
 * Live2D Configuration Store State
 */
interface Live2DConfigState {
  modelInfo: ModelInfo | undefined;
  isLoading: boolean;
  error: Live2DError | null;
  
  // Actions
  setModelInfo: (info: ModelInfo | undefined) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: Live2DError | null) => void;
  updateModelConfig: (updates: Partial<ModelInfo>) => void;
  clearError: () => void;
}

/**
 * Live2D Interaction Store State
 */
interface Live2DInteractionState {
  isDragging: boolean;
  position: Position;
  isHovering: boolean;
  
  // Actions
  setDragging: (dragging: boolean) => void;
  setPosition: (position: Position) => void;
  setHovering: (hovering: boolean) => void;
  resetPosition: () => void;
}

/**
 * Default values
 */
const DEFAULT_POSITION: Position = { x: 0, y: 0 };

/**
 * Live2D Configuration Store
 */
export const useLive2DConfig = create<Live2DConfigState>((set, get) => ({
  modelInfo: undefined,
  isLoading: false,
  error: null,
  
  setModelInfo: (info) => {
    set({ modelInfo: info, error: null });
  },
  
  setIsLoading: (loading) => {
    set({ isLoading: loading });
  },
  
  setError: (error) => {
    set({ error, isLoading: false });
  },
  
  updateModelConfig: (updates) => {
    const currentInfo = get().modelInfo;
    if (currentInfo) {
      set({ 
        modelInfo: { ...currentInfo, ...updates },
        error: null 
      });
    }
  },
  
  clearError: () => {
    set({ error: null });
  },
}));

/**
 * Live2D Interaction Store
 */
export const useLive2DInteraction = create<Live2DInteractionState>((set) => ({
  isDragging: false,
  position: DEFAULT_POSITION,
  isHovering: false,
  
  setDragging: (dragging) => {
    set({ isDragging: dragging });
  },
  
  setPosition: (position) => {
    set({ position });
  },
  
  setHovering: (hovering) => {
    set({ isHovering: hovering });
  },
  
  resetPosition: () => {
    set({ position: DEFAULT_POSITION });
  },
}));

/**
 * Helper function to validate ModelInfo
 */
export const validateModelInfo = (info: Partial<ModelInfo>): ModelInfo | null => {
  if (!info.url) {
    return null;
  }
  
  return {
    url: info.url,
    kScale: info.kScale || 1.0,
    initialXshift: info.initialXshift || 0,
    initialYshift: info.initialYshift || 0,
    pointerInteractive: info.pointerInteractive ?? true,
    scrollToResize: info.scrollToResize ?? true,
    ...info,
  } as ModelInfo;
};

/**
 * Helper function to create Live2D error
 */
export const createLive2DError = (
  type: Live2DError['type'],
  message: string,
  details?: any,
  recoverable: boolean = false
): Live2DError => ({
  type,
  message,
  details,
  recoverable,
});

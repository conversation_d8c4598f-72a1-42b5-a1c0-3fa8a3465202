/**
 * Live2D TypeScript type definitions
 */

/**
 * Live2D Adapter interface for type safety
 */
export interface LAppAdapterInterface {
  setExpression: (expression: string) => void;
  getExpressionName: (index: number) => string | null;
  getExpressionCount: () => number;
  getModel: () => unknown;
}

// Global window extensions for Live2D
declare global {
  interface Window {
    getLAppAdapter?: () => LAppAdapterInterface | null;
    getLive2DManager?: () => unknown;
    LAppLive2DManager?: unknown;
    LAppDefine?: unknown;
    initializeLive2D?: () => void | Promise<void>;
  }
}

/**
 * Position interface for Live2D model positioning
 */
export interface Position {
  x: number;
  y: number;
}

/**
 * Model emotion mapping interface
 */
export interface EmotionMap {
  [key: string]: number | string;
}

/**
 * Motion weight mapping interface
 */
export interface MotionWeightMap {
  [key: string]: number;
}

/**
 * Tap motion mapping interface
 */
export interface TapMotionMap {
  [key: string]: MotionWeightMap;
}

/**
 * Live2D model information interface
 */
export interface ModelInfo {
  /** Model name */
  name?: string;

  /** Model description */
  description?: string;

  /** Model URL */
  url: string;

  /** Scale factor */
  kScale: number;

  /** Initial X position shift */
  initialXshift?: number;

  /** Initial Y position shift */
  initialYshift?: number;

  /** Idle motion group name */
  idleMotionGroupName?: string;

  /** Default emotion */
  defaultEmotion?: number | string;

  /** Emotion mapping configuration */
  emotionMap?: EmotionMap;

  /** Enable pointer interactivity */
  pointerInteractive?: boolean;

  /** Tap motion mapping configuration */
  tapMotions?: TapMotionMap;

  /** Enable scroll to resize */
  scrollToResize?: boolean;

  /** Initial scale */
  initialScale?: number;
}

/**
 * Interaction handlers interface
 */
export interface InteractionHandlers {
  onMouseDown: (e: React.MouseEvent | React.PointerEvent) => void;
  onMouseMove: (e: React.MouseEvent) => void;
  onMouseUp: (e: React.MouseEvent) => void;
  onMouseLeave: () => void;
}

/**
 * Live2D error types
 */
export type Live2DErrorType = 'SDK_INIT' | 'MODEL_LOAD' | 'RUNTIME' | 'AUDIO';

/**
 * Live2D error interface
 */
export interface Live2DError {
  type: Live2DErrorType;
  message: string;
  details?: any;
  recoverable: boolean;
}

/**
 * SDK configuration interface
 */
export interface SDKConfig {
  resourcePath: string;
  modelDirectory: string;
  modelFileName: string;
  scale: number;
  enableLipSync: boolean;
  enableExpressions: boolean;
}

export {};
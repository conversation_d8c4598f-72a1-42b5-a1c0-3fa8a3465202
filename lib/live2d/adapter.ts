/**
 * Live2D SDK Adapter
 * Provides a clean interface for interacting with the Live2D SDK
 */

import { RefObject } from 'react';
import { ModelInfo, Live2DError } from './types';

/**
 * Live2D Adapter Interface
 * Defines the contract for Live2D SDK operations
 */
export interface Live2DAdapter {
  initialize: (canvasRef: RefObject<HTMLCanvasElement>) => Promise<boolean>;
  loadModel: (modelUrl: string) => Promise<boolean>;
  setExpression: (expression: string | number) => void;
  getExpressionName: (index: number) => string | null;
  getExpressionCount: () => number;
  playAudioWithLipSync: (audioBase64: string) => Promise<void>;
  cleanup: () => void;
  isInitialized: () => boolean;
  getModel: () => any;
}

/**
 * Live2D SDK Adapter Implementation
 * Concrete implementation of the Live2D adapter interface
 */
export class Live2DSDKAdapter implements Live2DAdapter {
  private initialized = false;
  private canvas: HTMLCanvasElement | null = null;

  /**
   * Initialize the Live2D SDK with canvas
   */
  async initialize(canvasRef: RefObject<HTMLCanvasElement>): Promise<boolean> {
    try {
      if (!canvasRef.current) {
        throw new Error('Canvas reference is null');
      }

      this.canvas = canvasRef.current;
      
      // Check if Live2D SDK is available
      if (typeof window === 'undefined' || !window.initializeLive2D) {
        throw new Error('Live2D SDK not available');
      }

      // Initialize the SDK
      await window.initializeLive2D();
      this.initialized = true;
      
      return true;
    } catch (error) {
      console.error('Failed to initialize Live2D SDK:', error);
      this.initialized = false;
      return false;
    }
  }

  /**
   * Load a Live2D model
   */
  async loadModel(modelUrl: string): Promise<boolean> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized');
      }

      const adapter = (window as any).getLAppAdapter?.();
      if (!adapter) {
        throw new Error('LAppAdapter not available');
      }

      // Load model logic would go here
      // This is a simplified implementation
      return true;
    } catch (error) {
      console.error('Failed to load model:', error);
      return false;
    }
  }

  /**
   * Set expression for the model
   */
  setExpression(expression: string | number): void {
    try {
      if (!this.initialized) {
        console.warn('SDK not initialized');
        return;
      }

      const adapter = (window as any).getLAppAdapter?.();
      if (!adapter) {
        console.warn('LAppAdapter not available');
        return;
      }

      if (typeof expression === 'string') {
        adapter.setExpression(expression);
      } else if (typeof expression === 'number') {
        const expressionName = adapter.getExpressionName(expression);
        if (expressionName) {
          adapter.setExpression(expressionName);
        }
      }
    } catch (error) {
      console.error('Failed to set expression:', error);
    }
  }

  /**
   * Get expression name by index
   */
  getExpressionName(index: number): string | null {
    try {
      if (!this.initialized) {
        return null;
      }

      const adapter = (window as any).getLAppAdapter?.();
      if (!adapter) {
        return null;
      }

      return adapter.getExpressionName(index) || null;
    } catch (error) {
      console.error('Failed to get expression name:', error);
      return null;
    }
  }

  /**
   * Get total number of expressions
   */
  getExpressionCount(): number {
    try {
      if (!this.initialized) {
        return 0;
      }

      const adapter = (window as any).getLAppAdapter?.();
      if (!adapter) {
        return 0;
      }

      return adapter.getExpressionCount() || 0;
    } catch (error) {
      console.error('Failed to get expression count:', error);
      return 0;
    }
  }

  /**
   * Play audio with lip sync
   */
  async playAudioWithLipSync(audioBase64: string): Promise<void> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized');
      }

      // Convert base64 to audio blob
      const audioBlob = this.base64ToBlob(audioBase64, 'audio/wav');
      const audioUrl = URL.createObjectURL(audioBlob);

      // Create audio element
      const audio = new Audio(audioUrl);
      
      // Play audio with lip sync
      await new Promise<void>((resolve, reject) => {
        audio.onended = () => {
          URL.revokeObjectURL(audioUrl);
          resolve();
        };
        
        audio.onerror = () => {
          URL.revokeObjectURL(audioUrl);
          reject(new Error('Audio playback failed'));
        };
        
        audio.play().catch(reject);
      });
    } catch (error) {
      console.error('Failed to play audio with lip sync:', error);
      throw error;
    }
  }

  /**
   * Get the current model instance
   */
  getModel(): any {
    try {
      if (!this.initialized) {
        return null;
      }

      const adapter = (window as any).getLAppAdapter?.();
      return adapter?.getModel() || null;
    } catch (error) {
      console.error('Failed to get model:', error);
      return null;
    }
  }

  /**
   * Check if SDK is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    try {
      // Cleanup logic would go here
      this.initialized = false;
      this.canvas = null;
    } catch (error) {
      console.error('Failed to cleanup:', error);
    }
  }

  /**
   * Helper function to convert base64 to blob
   */
  private base64ToBlob(base64: string, mimeType: string): Blob {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }
}

/**
 * Global adapter instance
 */
let adapterInstance: Live2DAdapter | null = null;

/**
 * Get or create the global adapter instance
 */
export const getLive2DAdapter = (): Live2DAdapter => {
  if (!adapterInstance) {
    adapterInstance = new Live2DSDKAdapter();
  }
  return adapterInstance;
};

/**
 * Reset the global adapter instance
 */
export const resetLive2DAdapter = (): void => {
  if (adapterInstance) {
    adapterInstance.cleanup();
    adapterInstance = null;
  }
};

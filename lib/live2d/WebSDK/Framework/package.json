{"private": true, "scripts": {"build": "tsc", "test": "tsc --noEmit", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "clean": "<PERSON><PERSON><PERSON> dist"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "prettier": "^3.2.5", "rimraf": "^5.0.5", "typescript": "^5.4.2"}}
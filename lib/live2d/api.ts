/**
 * Live2D Public API
 * Clean interface for external usage of Live2D functionality
 */

import { ModelInfo, Position, Live2DError } from './types';
import { getLive2DAdapter } from './adapter';
import { validateModelInfo, createLive2DError } from './validation';
import { useLive2DConfig, useLive2DInteraction } from './store';

/**
 * Live2D API Configuration
 */
export interface Live2DAPIConfig {
  enableLogging?: boolean;
  autoInitialize?: boolean;
  errorHandler?: (error: Live2DError) => void;
}

/**
 * Live2D API Class
 * Provides a clean interface for controlling Live2D models
 */
export class Live2DAPI {
  private config: Live2DAPIConfig;
  private adapter = getLive2DAdapter();

  constructor(config: Live2DAPIConfig = {}) {
    this.config = {
      enableLogging: false,
      autoInitialize: true,
      ...config,
    };
  }

  /**
   * Set expression by name or index
   */
  setExpression(expression: string | number, logMessage?: string): boolean {
    try {
      this.adapter.setExpression(expression);
      
      if (this.config.enableLogging && logMessage) {
        console.log(logMessage);
      }
      
      return true;
    } catch (error) {
      const live2dError = createLive2DError(
        'RUNTIME',
        `Failed to set expression: ${expression}`,
        error
      );
      
      this.handleError(live2dError);
      return false;
    }
  }

  /**
   * Reset expression to default
   */
  resetExpression(): boolean {
    try {
      const expressionCount = this.adapter.getExpressionCount();
      if (expressionCount > 0) {
        const defaultExpression = this.adapter.getExpressionName(0);
        if (defaultExpression) {
          return this.setExpression(defaultExpression, 'Reset to default expression');
        }
      }
      return false;
    } catch (error) {
      const live2dError = createLive2DError(
        'RUNTIME',
        'Failed to reset expression',
        error
      );
      
      this.handleError(live2dError);
      return false;
    }
  }

  /**
   * Get available expressions
   */
  getExpressions(): string[] {
    try {
      const count = this.adapter.getExpressionCount();
      const expressions: string[] = [];
      
      for (let i = 0; i < count; i++) {
        const name = this.adapter.getExpressionName(i);
        if (name) {
          expressions.push(name);
        }
      }
      
      return expressions;
    } catch (error) {
      this.handleError(createLive2DError(
        'RUNTIME',
        'Failed to get expressions',
        error
      ));
      return [];
    }
  }

  /**
   * Play audio with lip sync
   */
  async playAudio(audioBase64: string): Promise<boolean> {
    try {
      await this.adapter.playAudioWithLipSync(audioBase64);
      return true;
    } catch (error) {
      const live2dError = createLive2DError(
        'AUDIO',
        'Failed to play audio',
        error,
        true
      );
      
      this.handleError(live2dError);
      return false;
    }
  }

  /**
   * Check if SDK is ready
   */
  isReady(): boolean {
    return this.adapter.isInitialized();
  }

  /**
   * Get current model info
   */
  getModelInfo(): ModelInfo | undefined {
    return useLive2DConfig.getState().modelInfo;
  }

  /**
   * Update model configuration
   */
  updateModelConfig(updates: Partial<ModelInfo>): boolean {
    try {
      const validation = validateModelInfo(updates);
      if (!validation.isValid) {
        throw new Error(`Invalid model config: ${validation.errors.join(', ')}`);
      }

      useLive2DConfig.getState().updateModelConfig(updates);
      return true;
    } catch (error) {
      this.handleError(createLive2DError(
        'RUNTIME',
        'Failed to update model config',
        error
      ));
      return false;
    }
  }

  /**
   * Get current interaction state
   */
  getInteractionState(): { isDragging: boolean; position: Position; isHovering: boolean } {
    const state = useLive2DInteraction.getState();
    return {
      isDragging: state.isDragging,
      position: state.position,
      isHovering: state.isHovering,
    };
  }

  /**
   * Handle errors
   */
  private handleError(error: Live2DError): void {
    if (this.config.errorHandler) {
      this.config.errorHandler(error);
    } else {
      console.error('Live2D API Error:', error);
    }
  }
}

/**
 * Global API instance
 */
let apiInstance: Live2DAPI | null = null;

/**
 * Get or create global API instance
 */
export const getLive2DAPI = (config?: Live2DAPIConfig): Live2DAPI => {
  if (!apiInstance) {
    apiInstance = new Live2DAPI(config);
  }
  return apiInstance;
};

/**
 * Utility functions for common operations
 */
export const Live2DUtils = {
  /**
   * Create a simple model configuration
   */
  createSimpleModelConfig: (url: string, scale: number = 1.0): ModelInfo => ({
    url,
    kScale: scale,
    pointerInteractive: true,
    scrollToResize: true,
    initialXshift: 0,
    initialYshift: 0,
  }),

  /**
   * Validate model URL format
   */
  isValidModelUrl: (url: string): boolean => {
    try {
      const urlObj = new URL(url);
      return urlObj.pathname.endsWith('.model3.json');
    } catch {
      return false;
    }
  },

  /**
   * Extract model name from URL
   */
  getModelNameFromUrl: (url: string): string => {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const lastSlash = pathname.lastIndexOf('/');
      const fileName = pathname.substring(lastSlash + 1);
      return fileName.replace('.model3.json', '');
    } catch {
      return 'Unknown Model';
    }
  },

  /**
   * Create error handler that logs to console
   */
  createConsoleErrorHandler: (prefix: string = '[Live2D]') => (error: Live2DError) => {
    console.error(`${prefix} ${error.type}:`, error.message, error.details);
  },
};

/**
 * React hooks for API usage
 */
export const useLive2DAPI = (config?: Live2DAPIConfig) => {
  const api = getLive2DAPI(config);
  
  return {
    setExpression: api.setExpression.bind(api),
    resetExpression: api.resetExpression.bind(api),
    getExpressions: api.getExpressions.bind(api),
    playAudio: api.playAudio.bind(api),
    isReady: api.isReady.bind(api),
    getModelInfo: api.getModelInfo.bind(api),
    updateModelConfig: api.updateModelConfig.bind(api),
    getInteractionState: api.getInteractionState.bind(api),
  };
};

export default Live2DAPI;

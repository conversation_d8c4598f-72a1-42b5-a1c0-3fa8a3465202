# Live2D 迁移问题总结

## 问题概述

在将 Live2D 功能从 `old-assets` 迁移到新的 Next.js 项目结构过程中，遇到了一系列初始化和配置问题。

## 主要问题及解决方案

### 1. TypeScript 导出类型错误

**问题**: `isolatedModules` 模式下不能重新导出类型和值
```
Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.
```

**解决方案**: 分离类型和值的导出
```typescript
// 错误的方式
export { Live2DAdapter, Live2DSDKAdapter } from '@/lib/live2d/adapter';

// 正确的方式
export { Live2DSDKAdapter } from '@/lib/live2d/adapter';
export type { Live2DAdapter } from '@/lib/live2d/adapter';
```

### 2. Live2D Core 库未加载

**问题**: `Live2DCubismCore is not defined`
```
ReferenceError: Live2DCubismCore is not defined
```

**原因**: Live2D Core 库需要在 Framework 初始化之前加载

**解决方案**: 在 HTML 中预加载 Core 库
```html
<!-- app/layout.tsx -->
<head>
  <script src="/lib/live2d/WebSDK/Core/live2dcubismcore.min.js" async></script>
</head>
```

### 3. Canvas 元素初始化时机问题

**问题**: `Cannot read properties of null (reading 'getId')`

**原因**: 
- Canvas 元素在 React 组件挂载时可能还没有正确的尺寸
- Live2D Framework 在 Canvas 准备好之前就被初始化

**解决方案**: 
1. 动态设置 Canvas 尺寸
2. 延迟初始化直到 Canvas 准备好

```typescript
// 确保 canvas 有正确的尺寸
useEffect(() => {
  const canvas = canvasRef.current;
  const container = containerRef.current;
  
  if (canvas && container) {
    const resizeCanvas = () => {
      const rect = container.getBoundingClientRect();
      canvas.width = rect.width || 800;
      canvas.height = rect.height || 600;
    };
    resizeCanvas();
  }
}, [canvasRef, containerRef]);
```

### 4. Framework 初始化顺序问题

**问题**: CubismFramework.getIdManager() 返回 null

**原因**: 
- Live2D Core 库加载完成但 Framework 没有正确初始化
- `initializeLive2D` 函数没有被正确调用

**解决方案**: 
1. 确保正确的初始化顺序：Core → Framework → Model
2. 导出 `initializeLive2D` 到 window 对象
3. 在模型加载前确保 Framework 已初始化

```typescript
// lib/live2d/WebSDK/src/main.ts
export async function initializeLive2D(): Promise<void> {
  // 等待 Core 库可用
  const coreAvailable = await waitForLive2DCore();
  if (!coreAvailable) return;
  
  // 初始化 GL 管理器和 Delegate
  const glManager = LAppGlManager.getInstance();
  const delegate = LAppDelegate.getInstance();
  const initResult = delegate.initialize();
  
  if (!initResult) {
    console.error("Failed to initialize LAppDelegate");
    return;
  }
}

// 导出到 window 对象
(window as any).initializeLive2D = initializeLive2D;
```

### 5. 模块导入和依赖问题

**问题**: `initializeLive2D` 函数在 window 对象上不可用

**解决方案**: 在使用前导入 main.ts 模块
```typescript
// hooks/live2d/use-live2d-model.ts
import '@cubismsdksamples/main';
```

## 最终的初始化流程

1. **HTML 加载**: Core 库通过 script 标签预加载
2. **模块导入**: 导入 main.ts 确保 `initializeLive2D` 可用
3. **Canvas 准备**: 确保 Canvas 元素有正确的尺寸
4. **配置设置**: 设置模型路径和参数
5. **Framework 初始化**: 调用 `initializeLive2D()`
6. **模型加载**: 创建 Live2D 管理器并加载模型

## 关键学习点

### Next.js 特殊考虑
- SSR 兼容性：使用 `dynamic` 导入禁用 SSR
- 静态资源：Core 库需要放在 `public` 目录
- 模块加载：确保正确的导入顺序

### React 集成注意事项
- Canvas 生命周期：等待 DOM 元素完全挂载
- 状态管理：使用 Zustand 替代 Context
- 错误边界：实现适当的错误处理

### Live2D SDK 特性
- 初始化顺序：Core → Framework → Model
- 单例模式：管理器使用单例模式
- WebGL 依赖：需要有效的 WebGL 上下文

## 预防措施

1. **类型安全**: 使用 TypeScript 严格模式
2. **错误处理**: 实现完整的错误边界和验证
3. **初始化检查**: 在每个步骤验证前置条件
4. **调试日志**: 保留关键的初始化日志
5. **文档化**: 记录复杂的初始化流程

## 性能优化

1. **延迟加载**: 只在需要时初始化 Live2D
2. **资源管理**: 正确清理和释放资源
3. **缓存策略**: 避免重复初始化
4. **错误恢复**: 实现优雅的错误恢复机制

这次迁移的成功关键在于理解 Live2D SDK 的初始化要求，以及如何在现代 React/Next.js 环境中正确集成传统的 WebGL 库。

/**
 * Live2D Usage Examples
 * Demonstrates various ways to use the Live2D components
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Live2DClient, SimpleLive2D, useLive2DAPI } from './index';
import { ModelInfo } from '@/lib/live2d/types';
import { Live2DUtils } from '@/lib/live2d/api';

/**
 * Basic Live2D Example
 */
export const BasicLive2DExample: React.FC = () => {
  return (
    <div className="w-full h-96 border border-gray-300 rounded-lg">
      <SimpleLive2D
        modelUrl="https://example.com/models/hiyori/hiyori.model3.json"
        className="w-full h-full"
      />
    </div>
  );
};

/**
 * Advanced Live2D Example with Controls
 */
export const AdvancedLive2DExample: React.FC = () => {
  const [modelInfo, setModelInfo] = useState<ModelInfo>({
    url: "https://example.com/models/hiyori/hiyori.model3.json",
    kScale: 1.0,
    pointerInteractive: true,
    scrollToResize: true,
    initialXshift: 0,
    initialYshift: 0,
  });

  const api = useLive2DAPI({
    enableLogging: true,
    errorHandler: (error) => {
      console.error('Live2D Error:', error);
      // Handle error in UI
    },
  });

  const handleExpressionChange = useCallback((expression: string) => {
    api.setExpression(expression, `Changed expression to: ${expression}`);
  }, [api]);

  const handleResetExpression = useCallback(() => {
    api.resetExpression();
  }, [api]);

  const handleScaleChange = useCallback((scale: number) => {
    setModelInfo(prev => ({ ...prev, kScale: scale }));
  }, []);

  const expressions = api.getExpressions();

  return (
    <div className="space-y-4">
      {/* Live2D Display */}
      <div className="w-full h-96 border border-gray-300 rounded-lg">
        <Live2DClient
          modelInfo={modelInfo}
          className="w-full h-full"
          fallback={
            <div className="flex items-center justify-center w-full h-full bg-red-50">
              <div className="text-red-600">Failed to load Live2D model</div>
            </div>
          }
        />
      </div>

      {/* Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Expression Controls */}
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">Expressions</h3>
          <div className="flex flex-wrap gap-2">
            {expressions.map((expression) => (
              <button
                key={expression}
                onClick={() => handleExpressionChange(expression)}
                className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                {expression}
              </button>
            ))}
            <button
              onClick={handleResetExpression}
              className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Reset
            </button>
          </div>
        </div>

        {/* Scale Control */}
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">Scale</h3>
          <div className="flex items-center space-x-2">
            <input
              type="range"
              min="0.5"
              max="3.0"
              step="0.1"
              value={modelInfo.kScale}
              onChange={(e) => handleScaleChange(parseFloat(e.target.value))}
              className="flex-1"
            />
            <span className="text-sm font-mono">{modelInfo.kScale.toFixed(1)}</span>
          </div>
        </div>
      </div>

      {/* Settings */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Settings</h3>
        <div className="flex flex-wrap gap-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={modelInfo.pointerInteractive}
              onChange={(e) => setModelInfo(prev => ({ 
                ...prev, 
                pointerInteractive: e.target.checked 
              }))}
            />
            <span>Pointer Interactive</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={modelInfo.scrollToResize}
              onChange={(e) => setModelInfo(prev => ({ 
                ...prev, 
                scrollToResize: e.target.checked 
              }))}
            />
            <span>Scroll to Resize</span>
          </label>
        </div>
      </div>
    </div>
  );
};

/**
 * Multiple Models Example
 */
export const MultipleModelsExample: React.FC = () => {
  const [selectedModel, setSelectedModel] = useState(0);

  const models = [
    {
      name: "Hiyori",
      url: "https://example.com/models/hiyori/hiyori.model3.json",
      scale: 1.0,
    },
    {
      name: "Mark",
      url: "https://example.com/models/mark/mark.model3.json",
      scale: 1.2,
    },
    {
      name: "Natori",
      url: "https://example.com/models/natori/natori.model3.json",
      scale: 0.8,
    },
  ];

  const currentModel = models[selectedModel];

  return (
    <div className="space-y-4">
      {/* Model Selector */}
      <div className="flex space-x-2">
        {models.map((model, index) => (
          <button
            key={model.name}
            onClick={() => setSelectedModel(index)}
            className={`px-4 py-2 rounded transition-colors ${
              selectedModel === index
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            {model.name}
          </button>
        ))}
      </div>

      {/* Live2D Display */}
      <div className="w-full h-96 border border-gray-300 rounded-lg">
        <Live2DClient
          key={currentModel.url} // Force re-render when model changes
          modelInfo={Live2DUtils.createSimpleModelConfig(currentModel.url, currentModel.scale)}
          className="w-full h-full"
        />
      </div>

      {/* Model Info */}
      <div className="text-sm text-gray-600">
        <p><strong>Current Model:</strong> {currentModel.name}</p>
        <p><strong>Scale:</strong> {currentModel.scale}</p>
        <p><strong>URL:</strong> {currentModel.url}</p>
      </div>
    </div>
  );
};

/**
 * Custom Error Handling Example
 */
export const CustomErrorHandlingExample: React.FC = () => {
  const [error, setError] = useState<string | null>(null);

  const customErrorFallback = (
    <div className="flex flex-col items-center justify-center w-full h-full bg-red-50 border border-red-200 rounded-lg">
      <div className="text-red-600 text-lg font-semibold mb-2">
        Live2D Error
      </div>
      <div className="text-red-500 text-sm mb-4">
        {error || 'Failed to load Live2D model'}
      </div>
      <button
        onClick={() => {
          setError(null);
          window.location.reload();
        }}
        className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
      >
        Retry
      </button>
    </div>
  );

  return (
    <div className="w-full h-96 border border-gray-300 rounded-lg">
      <Live2DClient
        modelUrl="https://invalid-url.com/model.model3.json" // Intentionally invalid
        className="w-full h-full"
        fallback={customErrorFallback}
      />
    </div>
  );
};

export default {
  BasicLive2DExample,
  AdvancedLive2DExample,
  MultipleModelsExample,
  CustomErrorHandlingExample,
};

/**
 * Live2D Component
 * Main React component for Live2D model rendering and interaction
 */

'use client';

import React, { memo, useRef, useEffect } from 'react';
import { useLive2DConfig } from '@/lib/live2d/store';
import { useLive2DModel } from '@/hooks/live2d/use-live2d-model';
import { useLive2DResize } from '@/hooks/live2d/use-live2d-resize';
import { useLive2DExpression, resetExpressionWithModelInfo } from '@/hooks/live2d/use-live2d-expression';
import { ModelInfo } from '@/lib/live2d/types';
import { cn } from '@/lib/utils';

interface Live2DProps {
  /** Additional CSS classes */
  className?: string;
  /** Model configuration */
  modelUrl?: string;
  /** Whether sidebar is shown (affects layout) */
  showSidebar?: boolean;
  /** Custom model info (overrides modelUrl) */
  modelInfo?: ModelInfo;
}

/**
 * Live2D Component
 * Renders a Live2D model with interaction capabilities
 */
export const Live2D = memo<Live2DProps>(({
  className,
  modelUrl,
  showSidebar,
  modelInfo: customModelInfo,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { modelInfo: storeModelInfo, setModelInfo } = useLive2DConfig();
  const { setExpression, resetExpression } = useLive2DExpression();

  // Use custom model info if provided, otherwise use store model info
  const effectiveModelInfo = customModelInfo || storeModelInfo;

  // Set model info from URL if provided
  useEffect(() => {
    if (modelUrl && !customModelInfo) {
      const newModelInfo: ModelInfo = {
        url: modelUrl,
        kScale: 1.0,
        pointerInteractive: true,
        scrollToResize: true,
      };
      setModelInfo(newModelInfo);
    }
  }, [modelUrl, customModelInfo, setModelInfo]);

  // Get canvasRef from useLive2DResize
  const { canvasRef, handleResize } = useLive2DResize({
    containerRef,
    modelInfo: effectiveModelInfo,
    showSidebar,
  });

  // Pass canvasRef to useLive2DModel
  const { isDragging, handlers } = useLive2DModel({
    modelInfo: effectiveModelInfo,
    canvasRef,
  });

  // Handle pointer down events
  const handlePointerDown = (e: React.PointerEvent) => {
    handlers.onMouseDown(e);
  };

  // Expose functions to window for external control (optional)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).live2dSetExpression = (value: string | number) => {
        setExpression(value, `External call: Set expression to ${value}`);
      };
      
      (window as any).live2dResetExpression = () => {
        resetExpressionWithModelInfo(effectiveModelInfo, setExpression);
      };

      return () => {
        delete (window as any).live2dSetExpression;
        delete (window as any).live2dResetExpression;
      };
    }
  }, [setExpression, effectiveModelInfo]);

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative w-full h-full overflow-hidden',
        'bg-transparent',
        className
      )}
      style={{
        cursor: isDragging ? 'grabbing' : 'default',
      }}
      onPointerDown={handlePointerDown}
      {...handlers}
    >
      <canvas
        id="canvas"
        ref={canvasRef}
        width={800}
        height={600}
        className={cn(
          'block w-full h-full',
          'touch-none select-none',
          isDragging ? 'cursor-grabbing' : 'cursor-default'
        )}
        style={{
          display: 'block',
        }}
      />
    </div>
  );
});

Live2D.displayName = 'Live2D';

/**
 * Live2D Error Boundary Component
 */
interface Live2DErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

interface Live2DErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class Live2DErrorBoundary extends React.Component<
  Live2DErrorBoundaryProps,
  Live2DErrorBoundaryState
> {
  constructor(props: Live2DErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): Live2DErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Live2D Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="flex items-center justify-center w-full h-full bg-gray-100 rounded-lg">
            <div className="text-center p-4">
              <div className="text-red-500 text-lg font-semibold mb-2">
                Live2D Error
              </div>
              <div className="text-gray-600 text-sm">
                Failed to load Live2D model
              </div>
              <button
                onClick={() => this.setState({ hasError: false })}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        )
      );
    }

    return this.props.children;
  }
}

/**
 * Live2D with Error Boundary
 * Wrapper component that includes error boundary
 */
export const Live2DWithErrorBoundary = memo<Live2DProps & { fallback?: React.ReactNode }>(
  ({ fallback, ...props }) => (
    <Live2DErrorBoundary fallback={fallback}>
      <Live2D {...props} />
    </Live2DErrorBoundary>
  )
);

Live2DWithErrorBoundary.displayName = 'Live2DWithErrorBoundary';

export default Live2D;

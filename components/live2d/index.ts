/**
 * Live2D Components and Hooks Export
 * Main entry point for Live2D functionality
 */

// Components
export { Live2D, Live2DErrorBoundary, Live2DWithErrorBoundary } from './live2d';
export { Live2DClient, SimpleLive2D } from './live2d-client';

// Hooks
export { useLive2DExpression, resetExpressionWithModelInfo } from '@/hooks/live2d/use-live2d-expression';
export { useLive2DModel } from '@/hooks/live2d/use-live2d-model';
export { useLive2DResize, applyScale } from '@/hooks/live2d/use-live2d-resize';

// Store and Types
export { useLive2DConfig, useLive2DInteraction, validateModelInfo, createLive2DError } from '@/lib/live2d/store';
export type {
  ModelInfo,
  Position,
  EmotionMap,
  MotionWeightMap,
  TapMotionMap,
  InteractionHandlers,
  Live2DError,
  Live2DErrorType,
  SDKConfig,
} from '@/lib/live2d/types';

// API and Utilities
export { Live2DAPI, getLive2DAPI, Live2DUtils, useLive2DAPI } from '@/lib/live2d/api';
export { Live2DAdapter, Live2DSDKAdapter, getLive2DAdapter, resetLive2DAdapter } from '@/lib/live2d/adapter';
export {
  validateModelInfo as validateModelInfoUtil,
  validateCanvas,
  createLive2DError as createLive2DErrorUtil,
  createSDKInitError,
  createModelLoadError,
  createRuntimeError,
  createAudioError,
  isRecoverableError,
  getUserFriendlyErrorMessage,
  logValidationResult,
  safeExecute,
  safeExecuteAsync,
} from '@/lib/live2d/validation';

// Examples (for development/documentation)
export { default as Live2DExamples } from './examples';

// Default export
export { Live2DClient as default } from './live2d-client';

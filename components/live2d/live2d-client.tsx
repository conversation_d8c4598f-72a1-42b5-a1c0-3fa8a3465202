/**
 * Live2D Client Component
 * Client-side wrapper for Live2D component to handle SSR compatibility
 */

'use client';

import dynamic from 'next/dynamic';
import { memo } from 'react';
import { ModelInfo } from '@/lib/live2d/types';

interface Live2DClientProps {
  /** Additional CSS classes */
  className?: string;
  /** Model configuration */
  modelUrl?: string;
  /** Whether sidebar is shown (affects layout) */
  showSidebar?: boolean;
  /** Custom model info (overrides modelUrl) */
  modelInfo?: ModelInfo;
  /** Loading component */
  loading?: React.ReactNode;
  /** Error fallback component */
  fallback?: React.ReactNode;
}

/**
 * Loading component for Live2D
 */
const Live2DLoading = () => (
  <div className="flex items-center justify-center w-full h-full bg-gray-50 rounded-lg animate-pulse">
    <div className="text-center">
      <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
      <div className="text-gray-600 text-sm">Loading Live2D...</div>
    </div>
  </div>
);

/**
 * Dynamically import Live2D component with SSR disabled
 */
const Live2DComponent = dynamic(
  () => import('./live2d').then((mod) => ({ default: mod.Live2DWithErrorBoundary })),
  {
    ssr: false,
    loading: () => <Live2DLoading />,
  }
);

/**
 * Client-side Live2D component wrapper
 * Handles SSR compatibility and provides loading states
 */
export const Live2DClient = memo<Live2DClientProps>(({
  className,
  modelUrl,
  showSidebar,
  modelInfo,
  loading,
  fallback,
}) => {
  return (
    <Live2DComponent
      className={className}
      modelUrl={modelUrl}
      showSidebar={showSidebar}
      modelInfo={modelInfo}
      fallback={fallback}
    />
  );
});

Live2DClient.displayName = 'Live2DClient';

/**
 * Simple Live2D component for basic usage
 */
export const SimpleLive2D = memo<{
  modelUrl: string;
  className?: string;
}>(({ modelUrl, className }) => (
  <Live2DClient
    modelUrl={modelUrl}
    className={className}
  />
));

SimpleLive2D.displayName = 'SimpleLive2D';

export default Live2DClient;

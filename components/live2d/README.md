# Live2D Components

A complete Live2D integration for Next.js applications with React components, hooks, and utilities.

## Features

- 🎭 **Full Live2D Support**: Complete integration with Live2D WebSDK
- ⚛️ **React Components**: Easy-to-use React components with TypeScript support
- 🎯 **Next.js Compatible**: SSR-safe with proper client-side rendering
- 🎨 **TailwindCSS Styling**: Modern styling with responsive design
- 🔧 **Custom Hooks**: Reusable hooks for model management, resizing, and expressions
- 🛡️ **Error Handling**: Comprehensive error boundaries and validation
- 📱 **Touch Support**: Full touch and pointer interaction support
- 🎵 **Audio Integration**: Lip sync and audio playback capabilities

## Quick Start

### Basic Usage

```tsx
import { SimpleLive2D } from '@/components/live2d';

export default function MyPage() {
  return (
    <div className="w-full h-96">
      <SimpleLive2D
        modelUrl="https://example.com/models/hiyori/hiyori.model3.json"
        className="w-full h-full"
      />
    </div>
  );
}
```

### Advanced Usage

```tsx
import { Live2DClient, useLive2DAPI } from '@/components/live2d';
import { ModelInfo } from '@/lib/live2d/types';

export default function AdvancedLive2D() {
  const modelInfo: ModelInfo = {
    url: "https://example.com/models/hiyori/hiyori.model3.json",
    kScale: 1.0,
    pointerInteractive: true,
    scrollToResize: true,
    defaultEmotion: "happy",
  };

  const api = useLive2DAPI();

  const handleExpressionChange = (expression: string) => {
    api.setExpression(expression);
  };

  return (
    <div className="space-y-4">
      <div className="w-full h-96">
        <Live2DClient
          modelInfo={modelInfo}
          className="w-full h-full"
        />
      </div>
      
      <div className="flex gap-2">
        <button onClick={() => handleExpressionChange('happy')}>
          Happy
        </button>
        <button onClick={() => handleExpressionChange('sad')}>
          Sad
        </button>
        <button onClick={() => api.resetExpression()}>
          Reset
        </button>
      </div>
    </div>
  );
}
```

## Components

### SimpleLive2D

The simplest way to display a Live2D model.

```tsx
<SimpleLive2D
  modelUrl="https://example.com/model.model3.json"
  className="w-full h-full"
/>
```

### Live2DClient

Full-featured Live2D component with all options.

```tsx
<Live2DClient
  modelInfo={modelInfo}
  showSidebar={false}
  className="w-full h-full"
  fallback={<div>Loading failed...</div>}
/>
```

### Live2DErrorBoundary

Error boundary for handling Live2D errors.

```tsx
<Live2DErrorBoundary fallback={<CustomErrorComponent />}>
  <Live2DClient modelUrl="..." />
</Live2DErrorBoundary>
```

## Hooks

### useLive2DAPI

Main API hook for controlling Live2D models.

```tsx
const api = useLive2DAPI({
  enableLogging: true,
  errorHandler: (error) => console.error(error),
});

// Set expression
api.setExpression('happy');

// Reset expression
api.resetExpression();

// Get available expressions
const expressions = api.getExpressions();

// Play audio with lip sync
await api.playAudio(audioBase64);
```

### useLive2DConfig

State management for Live2D configuration.

```tsx
const { modelInfo, setModelInfo, isLoading, error } = useLive2DConfig();
```

### useLive2DInteraction

State management for interaction state.

```tsx
const { isDragging, position, setDragging } = useLive2DInteraction();
```

## Types

### ModelInfo

```typescript
interface ModelInfo {
  url: string;                    // Model file URL
  kScale: number;                // Scale factor
  initialXshift?: number;        // Initial X offset
  initialYshift?: number;        // Initial Y offset
  pointerInteractive?: boolean;  // Enable interactions
  scrollToResize?: boolean;      // Enable scroll scaling
  defaultEmotion?: string | number; // Default expression
  tapMotions?: TapMotionMap;     // Tap gesture mappings
}
```

### Live2DError

```typescript
interface Live2DError {
  type: 'SDK_INIT' | 'MODEL_LOAD' | 'RUNTIME' | 'AUDIO';
  message: string;
  details?: any;
  recoverable: boolean;
}
```

## Utilities

### Live2DUtils

```tsx
import { Live2DUtils } from '@/lib/live2d/api';

// Create simple model config
const modelInfo = Live2DUtils.createSimpleModelConfig(
  'https://example.com/model.model3.json',
  1.0
);

// Validate model URL
const isValid = Live2DUtils.isValidModelUrl(url);

// Extract model name from URL
const name = Live2DUtils.getModelNameFromUrl(url);
```

## Error Handling

The Live2D system includes comprehensive error handling:

```tsx
// Custom error handler
const errorHandler = (error: Live2DError) => {
  switch (error.type) {
    case 'SDK_INIT':
      // Handle SDK initialization errors
      break;
    case 'MODEL_LOAD':
      // Handle model loading errors
      break;
    case 'RUNTIME':
      // Handle runtime errors
      break;
    case 'AUDIO':
      // Handle audio errors
      break;
  }
};

const api = useLive2DAPI({ errorHandler });
```

## Best Practices

1. **Always use client-side components** for Live2D (they're SSR-safe by default)
2. **Provide fallback components** for error states
3. **Validate model URLs** before using them
4. **Handle loading states** appropriately
5. **Use error boundaries** to catch and handle errors gracefully
6. **Clean up resources** when components unmount

## Troubleshooting

### Model not loading
- Check that the model URL is accessible
- Ensure the URL ends with `.model3.json`
- Verify CORS settings if loading from external domains

### WebGL errors
- Check browser WebGL support
- Ensure canvas element is properly mounted
- Check for conflicting WebGL contexts

### Performance issues
- Use appropriate model scales
- Limit the number of simultaneous models
- Implement proper cleanup in useEffect hooks

## Migration from Old Assets

This Live2D system has been migrated from the old-assets structure with the following improvements:

- ✅ Removed Electron dependencies
- ✅ Replaced React Context with Zustand stores
- ✅ Added TypeScript support throughout
- ✅ Implemented proper error handling
- ✅ Added Next.js SSR compatibility
- ✅ Modernized with TailwindCSS
- ✅ Created clean public API
- ✅ Added comprehensive validation

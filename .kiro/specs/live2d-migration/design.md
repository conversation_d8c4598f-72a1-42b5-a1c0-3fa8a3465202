# Design Document

## Overview

The Live2D migration design focuses on adapting the existing Live2D implementation from old-assets to work seamlessly with the new Next.js project structure. The design preserves all existing functionality while modernizing the code organization, state management, and integration patterns. The migration involves restructuring React components, replacing context-based state management with Zustand stores, removing Electron dependencies, and ensuring proper Next.js compatibility.

## Architecture

### High-Level Architecture

```
Next.js Application
├── Live2D Components Layer
│   ├── Live2D Main Component
│   ├── Custom Hooks (Model, Resize, Expression)
│   └── Utility Functions
├── State Management Layer (Zustand)
│   ├── Live2D Configuration Store
│   ├── Model State Store
│   └── Interaction State Store
├── Live2D SDK Layer
│   ├── WebSDK (Core + Framework)
│   ├── Application Layer (LAppDelegate, LAppManager)
│   └── Model Management
└── Browser APIs
    ├── WebGL Context
    ├── Canvas API
    └── Audio API
```

### Migration Strategy

The migration follows a layered approach:
1. **SDK Layer**: Copy WebSDK with minimal modifications
2. **State Layer**: Replace React Context with Zustand stores
3. **Component Layer**: Adapt React components for new structure
4. **Integration Layer**: Ensure Next.js compatibility

## Components and Interfaces

### Core Components

#### 1. Live2D Main Component
```typescript
interface Live2DProps {
  className?: string;
  modelUrl?: string;
  showSidebar?: boolean;
}

export const Live2D: React.FC<Live2DProps>
```

**Responsibilities:**
- Render Live2D canvas and container
- Coordinate between hooks and SDK
- Handle component lifecycle
- Manage event delegation

#### 2. Live2D Model Hook
```typescript
interface UseLive2DModelProps {
  modelInfo: ModelInfo | undefined;
  canvasRef: RefObject<HTMLCanvasElement>;
}

interface UseLive2DModelReturn {
  position: Position;
  isDragging: boolean;
  handlers: InteractionHandlers;
}

export const useLive2DModel: (props: UseLive2DModelProps) => UseLive2DModelReturn
```

**Responsibilities:**
- Handle model loading and initialization
- Manage drag and tap interactions
- Coordinate with SDK for model manipulation
- Handle hit area detection

#### 3. Live2D Resize Hook
```typescript
interface UseLive2DResizeProps {
  containerRef: RefObject<HTMLDivElement>;
  modelInfo?: ModelInfo;
  showSidebar?: boolean;
}

interface UseLive2DResizeReturn {
  canvasRef: RefObject<HTMLCanvasElement>;
  handleResize: () => void;
}

export const useLive2DResize: (props: UseLive2DResizeProps) => UseLive2DResizeReturn
```

**Responsibilities:**
- Handle canvas resizing and scaling
- Manage responsive behavior
- Handle wheel-based scaling
- Coordinate with container changes

#### 4. Live2D Expression Hook
```typescript
interface UseLive2DExpressionReturn {
  setExpression: (value: string | number, logMessage?: string) => void;
  resetExpression: () => void;
}

export const useLive2DExpression: () => UseLive2DExpressionReturn
```

**Responsibilities:**
- Manage expression changes
- Handle expression validation
- Provide expression API

### State Management Interfaces

#### 1. Live2D Configuration Store
```typescript
interface ModelInfo {
  url: string;
  kScale: number;
  scrollToResize: boolean;
  pointerInteractive: boolean;
  tapMotions?: Record<string, string>;
  defaultEmotion?: string | number;
  initialXshift?: number;
  initialYshift?: number;
}

interface Live2DConfigState {
  modelInfo: ModelInfo | undefined;
  setModelInfo: (info: ModelInfo) => void;
  updateModelConfig: (updates: Partial<ModelInfo>) => void;
}
```

#### 2. Live2D Interaction Store
```typescript
interface Live2DInteractionState {
  isDragging: boolean;
  position: Position;
  isHovering: boolean;
  setDragging: (dragging: boolean) => void;
  setPosition: (position: Position) => void;
  setHovering: (hovering: boolean) => void;
}
```

### SDK Integration Interfaces

#### 1. SDK Adapter Interface
```typescript
interface Live2DAdapter {
  initialize: (canvasRef: RefObject<HTMLCanvasElement>) => Promise<boolean>;
  loadModel: (modelUrl: string) => Promise<boolean>;
  setExpression: (expression: string | number) => void;
  getExpressionName: (index: number) => string | null;
  getExpressionCount: () => number;
  playAudioWithLipSync: (audioBase64: string) => Promise<void>;
  cleanup: () => void;
}
```

## Data Models

### Model Configuration
```typescript
interface ModelInfo {
  url: string;                    // Model file URL
  kScale: number;                // Scale factor
  scrollToResize: boolean;       // Enable scroll resizing
  pointerInteractive: boolean;   // Enable pointer interactions
  tapMotions?: Record<string, string>; // Hit area to motion mapping
  defaultEmotion?: string | number;    // Default expression
  initialXshift?: number;        // Initial X offset
  initialYshift?: number;        // Initial Y offset
}
```

### Interaction State
```typescript
interface Position {
  x: number;
  y: number;
}

interface InteractionHandlers {
  onMouseDown: (e: React.MouseEvent) => void;
  onMouseMove: (e: React.MouseEvent) => void;
  onMouseUp: (e: React.MouseEvent) => void;
  onMouseLeave: () => void;
}
```

### SDK Configuration
```typescript
interface SDKConfig {
  resourcePath: string;
  modelDirectory: string;
  modelFileName: string;
  scale: number;
  enableLipSync: boolean;
  enableExpressions: boolean;
}
```

## Error Handling

### Error Categories

1. **SDK Initialization Errors**
   - WebGL context creation failure
   - SDK loading failure
   - Canvas initialization failure

2. **Model Loading Errors**
   - Model file not found
   - Invalid model format
   - Resource loading failure

3. **Runtime Errors**
   - Expression setting failure
   - Audio playback failure
   - Interaction handling errors

### Error Handling Strategy

```typescript
interface Live2DError {
  type: 'SDK_INIT' | 'MODEL_LOAD' | 'RUNTIME' | 'AUDIO';
  message: string;
  details?: any;
  recoverable: boolean;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Live2DError | null;
  retry: () => void;
}
```

**Error Recovery:**
- Automatic retry for recoverable errors
- Fallback states for non-recoverable errors
- User notification for critical errors
- Graceful degradation when possible

## Testing Strategy

### Unit Testing
- **Zustand Stores**: Test state mutations and selectors
- **Custom Hooks**: Test hook behavior and side effects
- **Utility Functions**: Test pure functions and calculations
- **Error Handling**: Test error scenarios and recovery

### Integration Testing
- **Component Integration**: Test component interaction with stores
- **SDK Integration**: Test SDK initialization and model loading
- **Event Handling**: Test user interactions and responses
- **State Synchronization**: Test state consistency across components

### End-to-End Testing
- **Model Loading**: Test complete model loading flow
- **User Interactions**: Test drag, resize, and tap interactions
- **Expression Changes**: Test expression setting and transitions
- **Audio Integration**: Test lip sync functionality

### Testing Tools
- **Jest**: Unit and integration testing
- **React Testing Library**: Component testing
- **MSW**: API mocking for model loading
- **Playwright**: E2E testing

## Migration Implementation Plan

### Phase 1: SDK Migration
1. Copy WebSDK directory structure
2. Update import paths and module resolution
3. Ensure TypeScript compatibility
4. Test SDK initialization

### Phase 2: State Management Migration
1. Create Zustand stores to replace contexts
2. Implement store actions and selectors
3. Test store functionality
4. Document state management patterns

### Phase 3: Component Migration
1. Migrate main Live2D component
2. Update custom hooks with new state management
3. Remove Electron dependencies
4. Adapt styling to TailwindCSS

### Phase 4: Integration and Testing
1. Integrate components with Next.js
2. Handle SSR considerations
3. Implement error boundaries
4. Comprehensive testing

### Phase 5: API and Documentation
1. Create clean public API
2. Document usage patterns
3. Create example implementations
4. Performance optimization

## Next.js Specific Considerations

### Server-Side Rendering
- Live2D components must be client-side only
- Use dynamic imports with `ssr: false`
- Handle hydration carefully
- Implement loading states

### Asset Management
- Ensure WebSDK assets are properly served
- Configure Next.js for static asset handling
- Handle model file loading
- Optimize asset loading performance

### Performance Optimization
- Lazy load Live2D components
- Implement proper cleanup on unmount
- Use React.memo for expensive components
- Optimize re-renders with proper dependencies

## Security Considerations

### Content Security Policy
- Allow WebGL and canvas operations
- Handle audio playback permissions
- Secure model file loading
- Validate user inputs

### Resource Loading
- Validate model URLs
- Handle CORS for external resources
- Implement proper error handling for failed loads
- Sanitize configuration inputs
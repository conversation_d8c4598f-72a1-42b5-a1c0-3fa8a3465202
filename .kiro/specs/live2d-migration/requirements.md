# Requirements Document

## Introduction

This feature involves migrating the existing Live2D functionality from the old-assets directory to the new Next.js-based frontend project (advx-frontend-olv-based). The migration focuses on preserving all existing functionality while adapting the code structure to work with the new tech stack (React, Next.js, Zustand, TailwindCSS, Shadcn, TypeScript). The main challenges are file structure reorganization, dependency management, state management changes, and maintaining compatibility with the existing Live2D WebSDK.

## Requirements

### Requirement 1

**User Story:** As a developer, I want to migrate the Live2D WebSDK from old-assets to the new project structure so that the Live2D functionality is available in the new codebase.

#### Acceptance Criteria

1. <PERSON><PERSON><PERSON> migrating the WebSDK THEN the system SHALL preserve the existing WebSDK directory structure under the new project
2. WH<PERSON> copying SDK files THEN the system SHALL maintain all Core and Framework components without modification
3. W<PERSON><PERSON> integrating the SDK THEN the system SHALL ensure proper TypeScript compatibility
4. W<PERSON><PERSON> setting up the SDK THEN the system SHALL maintain the existing initialization and configuration logic
5. IF SDK modifications are necessary THEN they SHALL be documented in sdk_diff directory

### Requirement 2

**User Story:** As a developer, I want to migrate the Live2D React components to work with the new project structure so that the UI components integrate properly with Next.js.

#### Acceptance Criteria

1. WHEN migrating Live2D.tsx THEN the system SHALL adapt it to work without the old context dependencies
2. WHEN migrating the hooks THEN the system SHALL update import paths to match the new project structure
3. WHEN adapting components THEN the system SHALL replace old context providers with Zustand state management
4. WHEN updating imports THEN the system SHALL use the new @/ path mapping configuration
5. WHEN removing dependencies THEN the system SHALL identify and eliminate unused context and utility imports

### Requirement 3

**User Story:** As a developer, I want to replace the old state management system with Zustand so that the Live2D functionality uses the new project's state management approach.

#### Acceptance Criteria

1. WHEN replacing live2d-config-context THEN the system SHALL create equivalent Zustand stores for model configuration
2. WHEN replacing ai-state-context THEN the system SHALL create Zustand stores for AI state management
3. WHEN replacing mode-context THEN the system SHALL create Zustand stores for mode management
4. WHEN migrating state logic THEN the system SHALL preserve all existing state behaviors and interactions
5. WHEN updating components THEN the system SHALL replace useContext hooks with Zustand store hooks

### Requirement 4

**User Story:** As a developer, I want to preserve all existing Live2D functionality during migration so that no features are lost in the transition.

#### Acceptance Criteria

1. WHEN migrating drag functionality THEN the system SHALL maintain the existing drag behavior and thresholds
2. WHEN migrating resize functionality THEN the system SHALL preserve scroll-to-resize and smooth scaling
3. WHEN migrating expression control THEN the system SHALL maintain the existing expression API and behavior
4. WHEN migrating hit detection THEN the system SHALL preserve tap vs drag detection and hit area responses
5. WHEN migrating audio integration THEN the system SHALL maintain the existing lip sync and audio playback capabilities

### Requirement 5

**User Story:** As a developer, I want to remove dependencies on Electron-specific APIs so that the Live2D system works in a web-only environment.

#### Acceptance Criteria

1. WHEN removing Electron dependencies THEN the system SHALL identify all window.api and electron API usage
2. WHEN adapting pet mode logic THEN the system SHALL either remove or adapt Electron-specific hover behaviors
3. WHEN handling IPC communications THEN the system SHALL remove or replace IPC-related functionality
4. WHEN updating event handling THEN the system SHALL ensure all interactions work in standard web browsers
5. WHEN removing Electron code THEN the system SHALL maintain core functionality without breaking changes

### Requirement 6

**User Story:** As a developer, I want to adapt the file structure to follow Next.js conventions so that the code is organized according to modern React project standards.

#### Acceptance Criteria

1. WHEN organizing components THEN the system SHALL place Live2D components in appropriate directories under the new structure
2. WHEN organizing hooks THEN the system SHALL create a hooks directory structure that follows Next.js patterns
3. WHEN organizing utilities THEN the system SHALL place utility functions in appropriate utility directories
4. WHEN organizing types THEN the system SHALL create proper TypeScript type definitions in appropriate locations
5. WHEN organizing assets THEN the system SHALL ensure the WebSDK and related assets are properly accessible

### Requirement 7

**User Story:** As a developer, I want to maintain TypeScript compatibility and improve type safety so that the migrated code follows modern TypeScript best practices.

#### Acceptance Criteria

1. WHEN migrating TypeScript code THEN the system SHALL resolve all type errors and warnings
2. WHEN updating imports THEN the system SHALL ensure proper module resolution with the new tsconfig.json
3. WHEN defining interfaces THEN the system SHALL create proper type definitions for all Live2D related data structures
4. WHEN handling SDK integration THEN the system SHALL provide proper TypeScript declarations for SDK components
5. WHEN improving type safety THEN the system SHALL eliminate any @ts-ignore comments where possible

### Requirement 8

**User Story:** As a developer, I want to integrate TailwindCSS styling so that the Live2D components use the project's styling system.

#### Acceptance Criteria

1. WHEN styling components THEN the system SHALL replace any existing CSS with TailwindCSS classes where appropriate
2. WHEN maintaining layouts THEN the system SHALL ensure the Live2D canvas and container styling works with Tailwind
3. WHEN handling responsive design THEN the system SHALL use Tailwind responsive utilities
4. WHEN preserving functionality THEN the system SHALL ensure styling changes don't break Live2D interactions
5. WHEN optimizing styles THEN the system SHALL remove any unused CSS and style dependencies

### Requirement 9

**User Story:** As a developer, I want to ensure the migrated Live2D system works properly with Next.js SSR and client-side rendering so that it integrates seamlessly with the Next.js framework.

#### Acceptance Criteria

1. WHEN handling SSR THEN the system SHALL ensure Live2D components only render on the client side
2. WHEN managing hydration THEN the system SHALL prevent hydration mismatches related to Live2D content
3. WHEN loading resources THEN the system SHALL ensure proper asset loading in the Next.js environment
4. WHEN handling routing THEN the system SHALL ensure Live2D state persists appropriately across route changes
5. WHEN optimizing performance THEN the system SHALL implement proper loading and cleanup patterns

### Requirement 10

**User Story:** As a developer, I want to create a clean integration API so that the migrated Live2D system can be easily used throughout the Next.js application.

#### Acceptance Criteria

1. WHEN creating the main component THEN the system SHALL provide a simple Live2D component that can be imported and used
2. WHEN exposing functionality THEN the system SHALL provide hooks for controlling expressions, audio, and interactions
3. WHEN managing configuration THEN the system SHALL provide a clean way to configure model settings and behavior
4. WHEN handling errors THEN the system SHALL provide proper error boundaries and error handling
5. WHEN documenting usage THEN the system SHALL ensure the API is intuitive and follows React patterns